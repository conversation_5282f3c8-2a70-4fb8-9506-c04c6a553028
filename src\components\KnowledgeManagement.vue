<script setup lang="ts">
import type { KnowledgeItem, KnowledgeManagementEmits, KnowledgeManagementProps } from '../types/interfaces'
import { ElAutoResizer, ElButton, ElDialog, ElMessage, ElMessageBox, ElOption, ElRadio, ElRadioGroup, ElSelect, ElTableV2, ElTag, TableV2FixedDir } from 'element-plus'
import { computed, h, nextTick, onMounted, ref, watch } from 'vue'
import { getConfig, saveConfig } from '../utils/ipc'

const props = defineProps<KnowledgeManagementProps>()
const emit = defineEmits<KnowledgeManagementEmits>()

const dialogVisible = ref(false)
const editIndex = ref<number | null>(null)
const form = ref({ author: '', source: '', content: '' })
const formRef = ref()
const isLoading = ref(false)

const rules = {
  author: [
    { required: true, message: '请输入作者姓名', trigger: 'blur' },
    { min: 1, max: 50, message: '作者姓名长度在 1 到 50 个字符', trigger: 'blur' },
  ],
  content: [
    { required: true, message: '请输入知识内容', trigger: 'blur' },
    { min: 1, max: 1000, message: '内容长度在 1 到 1000 个字符', trigger: 'blur' },
  ],
  source: [
    { max: 200, message: '出处长度不能超过 200 个字符', trigger: 'blur' },
  ],
}

const knowledgeBase = ref<KnowledgeItem[]>([])
const tableKey = ref(0)

// 批量删除相关状态
const batchDeleteDialogVisible = ref(false)
const batchDeleteLoading = ref(false)
const selectedDeleteType = ref<'bySource' | 'all'>('bySource')
const selectedSourceIds = ref<string[]>([])

// 计算可用的云端数据源
const availableCloudSources = computed(() => {
  const sources = new Map<string, { id: string, name: string, count: number }>()

  knowledgeBase.value.forEach((item) => {
    if (item.dataSource === 'cloud' && item.sourceId && item.sourceName) {
      const existing = sources.get(item.sourceId)
      if (existing) {
        existing.count++
      }
      else {
        sources.set(item.sourceId, {
          id: item.sourceId,
          name: item.sourceName,
          count: 1,
        })
      }
    }
  })

  return Array.from(sources.values())
})

// 计算云端数据总数
const totalCloudCount = computed(() => {
  return knowledgeBase.value.filter(item => item.dataSource === 'cloud').length
})

function forceTableRerender() {
  tableKey.value++
}

watch(knowledgeBase, () => {
  nextTick(() => {
    forceTableRerender()
  })
}, { deep: true })

watch(() => props.syncLoading, (newVal, oldVal) => {
  if (oldVal === true && newVal === false) {
    loadKnowledgeBase()
  }
})

const columns = [
  {
    key: 'author',
    title: '作者',
    dataKey: 'author',
    width: 120,
    align: 'left' as const,
    tooltip: true,
  },
  {
    key: 'source',
    title: '出处',
    dataKey: 'source',
    width: 180,
    align: 'left' as const,
    tooltip: true,
  },
  {
    key: 'content',
    title: '内容',
    dataKey: 'content',
    width: 300,
    align: 'left' as const,
    tooltip: true,
  },
  {
    key: 'dataSource',
    title: '来源',
    dataKey: 'dataSource',
    width: 120,
    align: 'center' as const,
    cellRenderer: ({ rowData }: { rowData: KnowledgeItem }) => (
      h('div', { class: 'flex flex-col items-center' }, [
        h(ElTag, { type: rowData.dataSource === 'cloud' ? 'info' : 'success', size: 'small' }, () => (
          rowData.dataSource === 'cloud' ? '云端' : '本地'
        )),
        rowData.dataSource === 'cloud' && rowData.sourceName
          ? h('div', { class: 'mt-1 text-xs text-gray-500' }, rowData.sourceName)
          : null,
      ])
    ),
  },
  {
    key: 'actions',
    title: '操作',
    width: 160,
    align: 'left' as const,
    fixed: TableV2FixedDir.RIGHT,
    // 自定义渲染操作按钮
    cellRenderer: ({ rowIndex, rowData }: { rowIndex: number, rowData: KnowledgeItem }) => (
      h('div', {}, [
        h(ElButton, {
          size: 'small',
          disabled: rowData.dataSource === 'cloud',
          onClick: () => editKnowledge(rowIndex),
        }, () => '编辑'),
        h(ElButton, {
          size: 'small',
          type: 'danger',
          onClick: () => confirmDelete(rowIndex),
        }, () => '删除'),
      ])
    ),
  },
]

// 加载知识库数据
async function loadKnowledgeBase() {
  const result = await getConfig({
    showErrorMessage: true,
    errorMessage: '加载知识库失败',
    silent: false,
  })
  if (result.success && result.data) {
    knowledgeBase.value = result.data.knowledgeBase || []
    await nextTick()
    forceTableRerender()
  }
}

// 更新知识库数据
async function updateKnowledgeBase() {
  const configResult = await getConfig({ silent: true })
  if (!configResult.success || !configResult.data) {
    return
  }

  const config = { ...configResult.data, knowledgeBase: knowledgeBase.value }

  await saveConfig(config, {
    showSuccessMessage: false,
    showErrorMessage: true,
    errorMessage: '保存失败',
    skipAutoSyncRestart: true,
  })
}

function openAddDialog() {
  editIndex.value = null
  form.value = { author: '', source: '', content: '' }
  dialogVisible.value = true
  setTimeout(() => {
    formRef.value?.clearValidate()
  }, 100)
}

function editKnowledge(index: number) {
  const item = knowledgeBase.value[index]
  if (item.dataSource === 'cloud') {
    ElMessage.error('云端词条不允许编辑')
    return
  }
  editIndex.value = index
  form.value = { ...knowledgeBase.value[index] }
  dialogVisible.value = true
  setTimeout(() => {
    formRef.value?.clearValidate()
  }, 100)
}

function saveKnowledge() {
  formRef.value.validate(async (valid: boolean) => {
    if (valid) {
      if (editIndex.value === null) {
        knowledgeBase.value.push({
          ...form.value,
          createTime: new Date().toLocaleString(),
          dataSource: 'local',
          sourceId: null,
        })
      }
      else {
        const currentItem = knowledgeBase.value[editIndex.value]
        if (currentItem.dataSource === 'cloud') {
          ElMessage.error('云端词条不允许编辑')
          return
        }
        knowledgeBase.value[editIndex.value] = {
          ...form.value,
          updateTime: new Date().toLocaleString(),
          dataSource: currentItem.dataSource || 'local',
        }
      }
      dialogVisible.value = false
      ElMessage.success(editIndex.value === null ? '新增成功' : '更新成功')
      await updateKnowledgeBase()
    }
    else {
      ElMessage.error('请检查输入内容')
    }
  })
}

function confirmDelete(index: number) {
  ElMessageBox.confirm(
    '确定要删除这条知识吗？',
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    },
  ).then(() => {
    deleteKnowledge(index)
  }).catch(() => {})
}

function deleteKnowledge(index: number) {
  knowledgeBase.value.splice(index, 1)
  updateKnowledgeBase()
  ElMessage.success('删除成功')
}

function handleSyncFromCloud() {
  emit('syncFromCloud')
}

// 批量删除相关函数
function openBatchDeleteDialog() {
  if (totalCloudCount.value === 0) {
    ElMessage.warning('没有云端数据可以删除')
    return
  }

  selectedDeleteType.value = 'bySource'
  selectedSourceIds.value = []
  batchDeleteDialogVisible.value = true
}

async function confirmBatchDelete() {
  if (selectedDeleteType.value === 'bySource' && selectedSourceIds.value.length === 0) {
    ElMessage.warning('请选择要删除的数据源')
    return
  }

  const deleteTypeText = selectedDeleteType.value === 'all' ? '所有云端数据' : '选中来源的云端数据'

  try {
    await ElMessageBox.confirm(
      `确定要删除${deleteTypeText}吗？此操作不可撤销。`,
      '批量删除确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
      },
    )

    await executeBatchDelete()
  }
  catch {
    // 用户取消删除
  }
}

async function executeBatchDelete() {
  try {
    batchDeleteLoading.value = true

    let result
    if (selectedDeleteType.value === 'all') {
      result = await window.require('electron').ipcRenderer.invoke('delete-all-cloud-data')
    }
    else {
      result = await window.require('electron').ipcRenderer.invoke('delete-cloud-data-by-source', selectedSourceIds.value)
    }

    if (result.success) {
      ElMessage.success(`删除成功！共删除 ${result.itemCount || 0} 条数据`)
      batchDeleteDialogVisible.value = false
      await loadKnowledgeBase() // 重新加载数据
    }
    else {
      ElMessage.error(`删除失败：${result.message}`)
    }
  }
  catch (error) {
    console.error('批量删除失败:', error)
    ElMessage.error('删除失败，请重试')
  }
  finally {
    batchDeleteLoading.value = false
  }
}

onMounted(() => {
  loadKnowledgeBase()
  isLoading.value = false
})
</script>

<template>
  <div class="knowledge-management-container">
    <div class="header-section">
      <div class="flex items-center">
        <h2 class="text-lg font-bold">
          知识库管理
        </h2>
        <span v-if="lastSyncTime" class="ml-2 text-sm text-gray-500">
          最后同步时间: {{ lastSyncTime }}
        </span>
      </div>
      <div class="flex gap-2">
        <ElButton
          type="info"
          :loading="syncLoading"
          @click="handleSyncFromCloud"
        >
          {{ syncLoading ? '同步中...' : '云端同步' }}
        </ElButton>
        <ElButton
          type="danger"
          plain
          :disabled="totalCloudCount === 0"
          @click="openBatchDeleteDialog"
        >
          批量删除
        </ElButton>
        <ElButton type="primary" @click="openAddDialog">
          新增知识
        </ElButton>
      </div>
    </div>

    <div class="table-section">
      <ElAutoResizer>
        <template #default="{ height, width }">
          <ElTableV2
            :key="tableKey"
            v-loading="isLoading"
            :data="knowledgeBase"
            :columns="columns"
            :width="width"
            :height="height"
          />
        </template>
      </ElAutoResizer>
    </div>
  </div>

  <ElDialog
    v-model="dialogVisible"
    :title="editIndex === null ? '新增知识' : '编辑知识'"
    width="600px"
  >
    <el-form ref="formRef" :model="form" :rules="rules" label-width="80px">
      <el-form-item label="作者" prop="author">
        <el-input v-model="form.author" placeholder="作者" />
      </el-form-item>
      <el-form-item label="出处" prop="source">
        <el-input v-model="form.source" placeholder="知识来源，如书籍、网站等" />
      </el-form-item>
      <el-form-item label="内容" prop="content">
        <el-input
          v-model="form.content"
          type="textarea"
          placeholder="输入知识内容"
          :rows="6"
          maxlength="1000"
          show-word-limit
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <ElButton @click="dialogVisible = false">
        取消
      </ElButton>
      <ElButton type="primary" @click="saveKnowledge">
        保存
      </ElButton>
    </template>
  </ElDialog>

  <!-- 批量删除对话框 -->
  <ElDialog
    v-model="batchDeleteDialogVisible"
    title="批量删除云端数据"
    width="500px"
  >
    <div class="batch-delete-content">
      <div class="mb-4">
        <p class="mb-3 text-sm text-gray-600">
          当前共有 <span class="text-blue-600 font-bold">{{ totalCloudCount }}</span> 条云端数据
        </p>

        <el-form label-width="100px">
          <el-form-item label="删除方式">
            <ElRadioGroup v-model="selectedDeleteType">
              <ElRadio value="bySource">
                按来源删除
              </ElRadio>
              <ElRadio value="all">
                删除全部
              </ElRadio>
            </ElRadioGroup>
          </el-form-item>

          <el-form-item
            v-if="selectedDeleteType === 'bySource'"
            label="选择来源"
          >
            <ElSelect
              v-model="selectedSourceIds"
              multiple
              placeholder="请选择要删除的数据源"
              style="width: 100%"
            >
              <ElOption
                v-for="source in availableCloudSources"
                :key="source.id"
                :label="`${source.name} (${source.count}条)`"
                :value="source.id"
              />
            </ElSelect>
          </el-form-item>
        </el-form>

        <div v-if="selectedDeleteType === 'all'" class="mt-3 border border-red-200 rounded bg-red-50 p-3">
          <p class="text-sm text-red-600">
            ⚠️ 将删除所有云端数据，此操作不可撤销！
          </p>
        </div>

        <div v-else-if="selectedSourceIds.length > 0" class="mt-3 border border-yellow-200 rounded bg-yellow-50 p-3">
          <p class="text-sm text-yellow-700">
            将删除来自以下数据源的数据：
          </p>
          <ul class="mt-2 text-sm">
            <li v-for="sourceId in selectedSourceIds" :key="sourceId" class="text-yellow-600">
              • {{ availableCloudSources.find(s => s.id === sourceId)?.name }}
              ({{ availableCloudSources.find(s => s.id === sourceId)?.count }}条)
            </li>
          </ul>
        </div>
      </div>
    </div>

    <template #footer>
      <ElButton @click="batchDeleteDialogVisible = false">
        取消
      </ElButton>
      <ElButton
        type="danger"
        :loading="batchDeleteLoading"
        @click="confirmBatchDelete"
      >
        {{ batchDeleteLoading ? '删除中...' : '确认删除' }}
      </ElButton>
    </template>
  </ElDialog>
</template>

<style scoped>
.knowledge-management-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.header-section {
  flex-shrink: 0;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.table-section {
  flex: 1;
  min-height: 0;
}
</style>
